import 'package:sqflite/sqflite.dart';
import 'package:unstack/models/tasks/task.model.dart';

class DatabaseService {
  static final DatabaseService instance = DatabaseService._instance();

  DatabaseService._instance();

  static Database? _db;

  String table = "tasks_table";
  String columnId = "id";
  String columnTitle = "title";
  String columnDescription = "description";
  String columnPriority = "priority";
  String columnPriorityIndex = "priorityIndex";
  String columnCreatedAt = "createdAt";
  String columnIsCompleted = "isCompleted";

  // Streak tracking
  String streakTable = "streak_table";
  String streakColumnId = "id";
  String streakColumnDate = "date";
  String streakColumnTotalTasks = "totalTasks";
  String streakColumnCompletedTasks = "completedTasks";
  String streakColumnAllTasksCompleted = "allTasksCompleted";
  String streakColumnLongestStreak = "longestStreak";
  String streakColumnCurrentStreak = "currentStreak";

  Future<Database?> get db async {
    if (_db != null) return _db;
    _db = await _initDB('tasks.db');
    return _db;
  }

  Future<Database> _initDB(String filePath) async {
    final dbPath = await getDatabasesPath();
    final path = '$dbPath/$filePath';

    final database = await openDatabase(
      path,
      version: 1,
      onCreate: _createDB,
    );

    await initialTasks(database);

    return database;
  }

  void _createDB(Database db, int version) async {
    await db.execute('''
      CREATE TABLE $table (
        $columnId TEXT PRIMARY KEY,
        $columnTitle TEXT NOT NULL,
        $columnDescription TEXT,
        $columnPriority TEXT NOT NULL,
        $columnCreatedAt TEXT NOT NULL,
        $columnIsCompleted INTEGER NOT NULL,
        $columnPriorityIndex INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE $streakTable (
        $streakColumnId INTEGER PRIMARY KEY AUTOINCREMENT,
        $streakColumnDate TEXT NOT NULL,
        $streakColumnTotalTasks INTEGER NOT NULL,
        $streakColumnCompletedTasks INTEGER NOT NULL,
        $streakColumnAllTasksCompleted INTEGER NOT NULL,
        $streakColumnLongestStreak INTEGER NOT NULL,
        $streakColumnCurrentStreak INTEGER NOT NULL
      )
    ''');
  }

  Future<void> initialTasks(Database database) async {
    // Only insert initial tasks if the database is empty
    final existingResults = await database.query(table);
    if (existingResults.isEmpty) {
      final tasks = TaskData.getSampleTasks();
      for (var task in tasks) {
        await database.insert(table, task.toJson(),
            conflictAlgorithm: ConflictAlgorithm.replace);
      }
    }
  }

  Future<void> closeDB() async {
    final db = await instance.db;
    db?.close();
  }

  Future<void> deleteDB() async {
    final dbPath = await getDatabasesPath();
    final path = '$dbPath/tasks.db';
    await deleteDatabase(path);
  }

  Future<void> deleteAll() async {
    final db = await instance.db;
    await db?.delete(table);
  }

  Future<List<Map<String, dynamic>>> getResults() async {
    final Database? db = await this.db;
    final List<Map<String, dynamic>> result = await db!.query(table);
    return result;
  }

  Future<List<Task>> getTasks() async {
    final results = await getResults();
    final tasks = results.map((result) => Task.fromJson(result)).toList();
    return tasks;
  }

  Future<void> insertTask(Task task) async {
    final db = await instance.db;
    await db?.insert(table, task.toJson(),
        conflictAlgorithm: ConflictAlgorithm.replace);
  }

  Future<void> updateTask(Task task) async {
    final db = await instance.db;
    await db?.update(table, task.toJson(),
        where: '$columnId = ?', whereArgs: [task.id]);
  }

  Future<void> deleteTask(String taskId) async {
    final db = await instance.db;
    await db?.delete(table, where: '$columnId = ?', whereArgs: [taskId]);
  }

  Future<void> deleteAllTasks() async {
    final db = await instance.db;
    await db?.delete(table);
  }

  Future<void> deleteStreakData() async {
    final db = await instance.db;
    await db?.delete(streakTable);
  }
}
